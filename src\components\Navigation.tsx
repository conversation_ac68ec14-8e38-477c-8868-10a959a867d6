import React, { useState } from 'react';

/**
 * Interface untuk item navigasi
 * Mendefinisikan struktur data untuk setiap tombol navigasi
 */
interface NavigationItem {
  id: string;
  label: string;
  icon?: string;
  path: string;
}

/**
 * Props untuk komponen Navigation
 */
interface NavigationProps {
  activeTab?: string;
  onTabChange?: (tabId: string) => void;
}

/**
 * Komponen Navigation horizontal dengan DaisyUI
 * Menampilkan tombol navigasi untuk HOME, KALENDER, PROFILE, INFORMATION, PRODUK, LAYANAN
 */
const Navigation: React.FC<NavigationProps> = ({ 
  activeTab = 'home', 
  onTabChange 
}) => {
  const [currentTab, setCurrentTab] = useState(activeTab);

  // Data navigasi dengan ikon dan label
  const navigationItems: NavigationItem[] = [
    {
      id: 'home',
      label: 'HOME',
      icon: '🏠',
      path: '/home'
    },
    {
      id: 'kalender',
      label: 'KALENDER',
      icon: '📅',
      path: '/kalender'
    },
    {
      id: 'profile',
      label: 'PROFILE',
      icon: '👤',
      path: '/profile'
    },
    {
      id: 'information',
      label: 'INFORMATION',
      icon: 'ℹ️',
      path: '/information'
    },
    {
      id: 'produk',
      label: 'PRODUK',
      icon: '📦',
      path: '/produk'
    },
    {
      id: 'layanan',
      label: 'LAYANAN',
      icon: '🛠️',
      path: '/layanan'
    }
  ];

  /**
   * Fungsi untuk menangani perubahan tab navigasi
   * @param tabId - ID tab yang dipilih
   */
  const handleTabChange = (tabId: string) => {
    setCurrentTab(tabId);
    if (onTabChange) {
      onTabChange(tabId);
    }
  };

  return (
    <div className="navigation-container">
      {/* Divider untuk memisahkan konten utama dengan navigasi */}
      <div className="divider my-0"></div>
      
      {/* Container navigasi dengan DaisyUI tabs */}
      <div className="bg-base-100 shadow-lg">
        <div className="container mx-auto px-4">
          <div className="tabs tabs-boxed bg-transparent justify-center gap-2 py-4">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                className={`
                  tab tab-lg font-semibold tracking-wide transition-all duration-300 ease-in-out
                  ${currentTab === item.id 
                    ? 'tab-active bg-primary text-primary-content shadow-md transform scale-105' 
                    : 'hover:bg-base-200 hover:text-primary hover:scale-102'
                  }
                  flex flex-col items-center gap-1 px-4 py-3 min-w-[80px]
                  11inch:min-w-[100px] 11inch:px-6 11inch:py-4
                  tablet-landscape:min-w-[90px] tablet-landscape:px-5
                `}
                onClick={() => handleTabChange(item.id)}
                aria-label={`Navigasi ke ${item.label}`}
              >
                {/* Ikon navigasi */}
                <span className="text-lg 11inch:text-xl tablet-landscape:text-lg">
                  {item.icon}
                </span>
                
                {/* Label navigasi */}
                <span className="text-xs font-bold 11inch:text-sm tablet-landscape:text-xs">
                  {item.label}
                </span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Navigation;
