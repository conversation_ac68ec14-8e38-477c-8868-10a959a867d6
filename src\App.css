/* App.css - Styling utama untuk aplikasi PPWA */

/* Container utama aplikasi */
.app-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Poppins', sans-serif;
}

/* Main content area */
main {
  flex: 1;
  width: 100%;
}

/* Responsive adjustments untuk tablet 11 inch landscape */
@media screen and (min-width: 1024px) and (max-width: 1366px) {
  .app-container {
    max-width: 1366px;
    margin: 0 auto;
  }

  /* Optimasi untuk layar tablet landscape */
  main {
    padding: 0;
  }
}

/* Responsive adjustments untuk tablet portrait */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .app-container {
    max-width: 100%;
  }
}

/* Responsive adjustments untuk mobile */
@media screen and (max-width: 767px) {
  .app-container {
    max-width: 100%;
  }

  main {
    padding: 0;
  }
}

/* Utility classes untuk responsive design */
.container-responsive {
  width: 100%;
  max-width: 1366px;
  margin: 0 auto;
  padding: 0 20px;
}

@media screen and (max-width: 767px) {
  .container-responsive {
    padding: 0 15px;
  }
}

/* Smooth transitions untuk semua elemen */
* {
  transition: all 0.3s ease;
}

/* Optimasi untuk touch devices */
@media (hover: none) and (pointer: coarse) {
  button, .btn, .tab {
    min-height: 44px;
    min-width: 44px;
  }
}