/* Homepage Component Styles - Responsive untuk tablet 11 inch landscape */

/* CSS Variables untuk animasi carousel */
:root {
  --item1-transform: translateX(-100%) translateY(-5%) scale(1.5);
  --item1-filter: blur(30px);
  --item1-zIndex: 11;
  --item1-opacity: 0;

  --item2-transform: translateX(0);
  --item2-filter: blur(0px);
  --item2-zIndex: 10;
  --item2-opacity: 1;

  --item3-transform: translate(50%, 10%) scale(0.8);
  --item3-filter: blur(10px);
  --item3-zIndex: 9;
  --item3-opacity: 1;

  --item4-transform: translate(90%, 20%) scale(0.5);
  --item4-filter: blur(30px);
  --item4-zIndex: 8;
  --item4-opacity: 1;

  --item5-transform: translate(120%, 30%) scale(0.3);
  --item5-filter: blur(40px);
  --item5-zIndex: 7;
  --item5-opacity: 0;
}

/* Container utama homepage */
.homepage-container {
  width: 100%;
  min-height: 100vh;
  font-family: 'Poppins', sans-serif;
  background-color: #ffffff;
}

/* Header styling */
.header {
  width: 1140px;
  max-width: 90%;
  display: flex;
  justify-content: space-between;
  margin: auto;
  height: 50px;
  align-items: center;
  padding: 0 20px;
}

.logo {
  font-weight: bold;
  font-size: 1.5rem;
  color: #693EFF;
}

.nav {
  display: flex;
  gap: 30px;
}

.nav a {
  text-decoration: none;
  color: #555;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav a:hover {
  color: #693EFF;
}

/* Divider SVG styling */
.divider-container {
  overflow: hidden;
  position: relative;
  height: 100px;
}

.divider-svg {
  position: absolute;
  width: 100%;
  height: 258px;
  transform: scale(1, 1);
  bottom: 0px;
  left: 0px;
  fill: rgb(255, 255, 255);
}

/* Carousel styling */
.carousel {
  position: relative;
  height: 800px;
  overflow: hidden;
  margin-top: -50px;
}

.carousel::before {
  width: 500px;
  height: 300px;
  content: '';
  background-image: linear-gradient(70deg, #DC422A, #693EFF);
  position: absolute;
  z-index: -1;
  border-radius: 20% 30% 80% 10%;
  filter: blur(150px);
  top: 50%;
  left: 50%;
  transform: translate(-10%, -50%);
  transition: 1s;
}

.carousel.showDetail::before {
  transform: translate(-100%, -50%) rotate(90deg);
  filter: blur(130px);
}

.carousel-list {
  position: absolute;
  width: 1140px;
  max-width: 90%;
  height: 80%;
  left: 50%;
  transform: translateX(-50%);
}

.carousel-item {
  position: absolute;
  left: 0%;
  width: 70%;
  height: 100%;
  font-size: 15px;
  transition: left 0.5s, opacity 0.5s, width 0.5s;
}

.carousel-item:nth-child(n + 6) {
  opacity: 0;
}

.carousel-item:nth-child(2) {
  z-index: 10;
  transform: translateX(0);
}

.product-image {
  width: 50%;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  transition: right 1.5s;
  object-fit: contain;
}

/* Introduce section styling */
.introduce {
  opacity: 0;
  pointer-events: none;
}

.carousel-item:nth-child(2) .introduce {
  opacity: 1;
  pointer-events: auto;
  width: 400px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transition: opacity 0.5s;
}

.title {
  font-size: 2em;
  font-weight: 500;
  line-height: 1em;
  color: #555;
}

.topic {
  font-size: 4em;
  font-weight: 500;
  color: #333;
  margin: 10px 0;
}

.description {
  font-size: small;
  color: #5559;
  margin: 20px 0;
  line-height: 1.6;
}

.see-more-btn {
  font-family: 'Poppins', sans-serif;
  margin-top: 1.2em;
  padding: 5px 0;
  border: none;
  border-bottom: 1px solid #555;
  background-color: transparent;
  font-weight: bold;
  letter-spacing: 3px;
  transition: background 0.5s;
  cursor: pointer;
}

.see-more-btn:hover {
  background: #eee;
  padding: 5px 10px;
}

/* Detail section styling */
.detail {
  opacity: 0;
  pointer-events: none;
}

.detail-title {
  font-size: 4em;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

.detail-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30px;
}

.specifications {
  display: flex;
  gap: 10px;
  width: 100%;
  border-top: 1px solid #5553;
  margin-top: 20px;
  padding-top: 20px;
  flex-wrap: wrap;
}

.spec-item {
  width: 90px;
  text-align: center;
  flex-shrink: 0;
}

.spec-item p:nth-child(1) {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.spec-item p:nth-child(2) {
  color: #666;
  font-size: 0.9rem;
}

.checkout {
  margin-top: 30px;
  display: flex;
  gap: 10px;
}

.checkout button {
  font-family: 'Poppins', sans-serif;
  padding: 10px 20px;
  letter-spacing: 2px;
  font-weight: 500;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Positioning untuk item carousel */
.carousel-item:nth-child(1) {
  transform: var(--item1-transform);
  filter: var(--item1-filter);
  z-index: var(--item1-zIndex);
  opacity: var(--item1-opacity);
  pointer-events: none;
}

.carousel-item:nth-child(3) {
  transform: var(--item3-transform);
  filter: var(--item3-filter);
  z-index: var(--item3-zIndex);
}

.carousel-item:nth-child(4) {
  transform: var(--item4-transform);
  filter: var(--item4-filter);
  z-index: var(--item4-zIndex);
}

.carousel-item:nth-child(5) {
  transform: var(--item5-transform);
  filter: var(--item5-filter);
  opacity: var(--item5-opacity);
  pointer-events: none;
}

/* Animasi untuk konten item aktif */
.carousel-item:nth-child(2) .title,
.carousel-item:nth-child(2) .topic,
.carousel-item:nth-child(2) .description,
.carousel-item:nth-child(2) .see-more-btn {
  opacity: 0;
  animation: showContent 0.5s 1s ease-in-out 1 forwards;
}

@keyframes showContent {
  from {
    transform: translateY(-30px);
    filter: blur(10px);
  }
  to {
    transform: translateY(0);
    opacity: 1;
    filter: blur(0px);
  }
}

.carousel-item:nth-child(2) .topic {
  animation-delay: 1.2s;
}

.carousel-item:nth-child(2) .description {
  animation-delay: 1.4s;
}

.carousel-item:nth-child(2) .see-more-btn {
  animation-delay: 1.6s;
}

/* Navigation arrows */
.arrows {
  position: absolute;
  bottom: 10px;
  width: 1140px;
  max-width: 90%;
  display: flex;
  justify-content: space-between;
  left: 50%;
  transform: translateX(-50%);
}

.nav-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-family: monospace;
  border: 1px solid #5555;
  font-size: large;
  background-color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background-color: #693EFF;
  color: white;
  transform: scale(1.1);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.back-btn {
  position: absolute;
  z-index: 100;
  bottom: 0%;
  left: 50%;
  transform: translateX(-50%);
  border: none;
  border-bottom: 1px solid #555;
  font-family: 'Poppins', sans-serif;
  font-weight: bold;
  letter-spacing: 3px;
  background-color: transparent;
  padding: 10px;
  opacity: 0;
  transition: opacity 0.5s;
  cursor: pointer;
}

.back-btn.visible {
  opacity: 1;
}

.back-btn:hover {
  background: #eee;
  padding: 10px 15px;
}

/* Animasi untuk transisi next */
.carousel.next .carousel-item:nth-child(1) {
  animation: transformFromPosition2 0.5s ease-in-out 1 forwards;
}

@keyframes transformFromPosition2 {
  from {
    transform: var(--item2-transform);
    filter: var(--item2-filter);
    opacity: var(--item2-opacity);
  }
}

.carousel.next .carousel-item:nth-child(2) {
  animation: transformFromPosition3 0.7s ease-in-out 1 forwards;
}

@keyframes transformFromPosition3 {
  from {
    transform: var(--item3-transform);
    filter: var(--item3-filter);
    opacity: var(--item3-opacity);
  }
}

.carousel.next .carousel-item:nth-child(3) {
  animation: transformFromPosition4 0.9s ease-in-out 1 forwards;
}

@keyframes transformFromPosition4 {
  from {
    transform: var(--item4-transform);
    filter: var(--item4-filter);
    opacity: var(--item4-opacity);
  }
}

.carousel.next .carousel-item:nth-child(4) {
  animation: transformFromPosition5 1.1s ease-in-out 1 forwards;
}

@keyframes transformFromPosition5 {
  from {
    transform: var(--item5-transform);
    filter: var(--item5-filter);
    opacity: var(--item5-opacity);
  }
}

/* Animasi untuk transisi previous */
.carousel.prev .carousel-item:nth-child(5) {
  animation: transformFromPosition4 0.5s ease-in-out 1 forwards;
}

.carousel.prev .carousel-item:nth-child(4) {
  animation: transformFromPosition3 0.7s ease-in-out 1 forwards;
}

.carousel.prev .carousel-item:nth-child(3) {
  animation: transformFromPosition2 0.9s ease-in-out 1 forwards;
}

.carousel.prev .carousel-item:nth-child(2) {
  animation: transformFromPosition1 1.1s ease-in-out 1 forwards;
}

@keyframes transformFromPosition1 {
  from {
    transform: var(--item1-transform);
    filter: var(--item1-filter);
    opacity: var(--item1-opacity);
  }
}

/* Show detail animations */
.carousel.showDetail .carousel-item:nth-child(3),
.carousel.showDetail .carousel-item:nth-child(4) {
  left: 100%;
  opacity: 0;
  pointer-events: none;
}

.carousel.showDetail .carousel-item:nth-child(2) {
  width: 100%;
}

.carousel.showDetail .carousel-item:nth-child(2) .introduce {
  opacity: 0;
  pointer-events: none;
}

.carousel.showDetail .carousel-item:nth-child(2) .product-image {
  right: 50%;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail {
  opacity: 1;
  width: 50%;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  text-align: right;
  pointer-events: auto;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail .detail-title,
.carousel.showDetail .carousel-item:nth-child(2) .detail .detail-description,
.carousel.showDetail .carousel-item:nth-child(2) .detail .specifications,
.carousel.showDetail .carousel-item:nth-child(2) .detail .checkout {
  opacity: 0;
  animation: showContent 0.5s 1s ease-in-out 1 forwards;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail .detail-description {
  animation-delay: 1.2s;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail .specifications {
  animation-delay: 1.4s;
}

.carousel.showDetail .carousel-item:nth-child(2) .detail .checkout {
  animation-delay: 1.6s;
}

.carousel.showDetail .nav-btn {
  opacity: 0;
  pointer-events: none;
}

/* Responsive Design untuk Tablet 11 inch Landscape (1366px) */
@media screen and (min-width: 1024px) and (max-width: 1366px) {
  .carousel {
    height: 700px;
  }

  .carousel-item {
    width: 80%;
  }

  .carousel-item:nth-child(2) .introduce {
    width: 350px;
  }

  .topic {
    font-size: 3.5em;
  }

  .detail-title {
    font-size: 3.5em;
  }

  .specifications {
    gap: 8px;
  }

  .spec-item {
    width: 80px;
  }
}

/* Responsive Design untuk Tablet Portrait dan iPad */
@media screen and (max-width: 1023px) {
  .carousel {
    height: 650px;
  }

  .carousel-item {
    width: 90%;
  }

  .carousel-item:nth-child(2) .introduce {
    width: 300px;
  }

  .topic {
    font-size: 3em;
  }

  .detail-title {
    font-size: 2.5em;
  }

  .carousel.showDetail .carousel-item:nth-child(2) .detail .specifications {
    overflow: auto;
  }

  .specifications {
    gap: 6px;
  }

  .spec-item {
    width: 70px;
    font-size: 0.85rem;
  }
}

/* Responsive Design untuk Mobile */
@media screen and (max-width: 767px) {
  .carousel {
    height: 600px;
  }

  .carousel-item {
    width: 100%;
    font-size: 10px;
  }

  .carousel-list {
    height: 100%;
  }

  .carousel-item:nth-child(2) .introduce {
    width: 50%;
  }

  .product-image {
    width: 40%;
  }

  .topic {
    font-size: 2.5em;
  }

  .title {
    font-size: 1.5em;
  }

  .description {
    height: 100px;
    overflow: auto;
  }

  .detail-title {
    font-size: 2em;
  }

  .carousel.showDetail .carousel-item:nth-child(2) .detail {
    backdrop-filter: blur(10px);
    font-size: small;
  }

  .carousel.showDetail .carousel-item:nth-child(2) .detail .detail-description {
    height: 100px;
    overflow: auto;
  }

  .carousel.showDetail .carousel-item:nth-child(2) .detail .checkout {
    display: flex;
    width: max-content;
    float: right;
  }

  .specifications {
    flex-direction: column;
    gap: 10px;
  }

  .spec-item {
    width: 100%;
    text-align: left;
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #eee;
  }

  .header {
    padding: 0 15px;
    height: 60px;
  }

  .nav {
    gap: 15px;
  }

  .nav a {
    font-size: 0.9rem;
  }
}
