import { useState } from 'react'
import './App.css'
import Homepage from './components/Homepage'
import Navigation from './components/Navigation'

/**
 * Komponen utama aplikasi PPWA
 * Mengintegrasikan Homepage dan Navigation dengan Capacitor untuk deployment Android
 */
function App() {
  // State untuk mengontrol tab navigasi aktif
  const [activeTab, setActiveTab] = useState('home');

  /**
   * Fungsi untuk menangani perubahan tab navigasi
   * @param tabId - ID tab yang dipilih
   */
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    console.log(`Navigasi ke tab: ${tabId}`);
    // TODO: Implementasi routing atau perubahan konten berdasarkan tab
  };

  /**
   * Fungsi untuk merender konten berdasarkan tab aktif
   * @returns JSX Element sesuai dengan tab yang dipilih
   */
  const renderContent = () => {
    switch (activeTab) {
      case 'home':
        return <Homepage />;
      case 'kalender':
        return (
          <div className="min-h-screen flex items-center justify-center bg-base-100">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-primary mb-4">📅 Kalender</h1>
              <p className="text-lg text-base-content">Halaman Kalender akan segera hadir</p>
            </div>
          </div>
        );
      case 'profile':
        return (
          <div className="min-h-screen flex items-center justify-center bg-base-100">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-primary mb-4">👤 Profile</h1>
              <p className="text-lg text-base-content">Halaman Profile akan segera hadir</p>
            </div>
          </div>
        );
      case 'information':
        return (
          <div className="min-h-screen flex items-center justify-center bg-base-100">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-primary mb-4">ℹ️ Information</h1>
              <p className="text-lg text-base-content">Halaman Information akan segera hadir</p>
            </div>
          </div>
        );
      case 'produk':
        return (
          <div className="min-h-screen flex items-center justify-center bg-base-100">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-primary mb-4">📦 Produk</h1>
              <p className="text-lg text-base-content">Halaman Produk akan segera hadir</p>
            </div>
          </div>
        );
      case 'layanan':
        return (
          <div className="min-h-screen flex items-center justify-center bg-base-100">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-primary mb-4">🛠️ Layanan</h1>
              <p className="text-lg text-base-content">Halaman Layanan akan segera hadir</p>
            </div>
          </div>
        );
      default:
        return <Homepage />;
    }
  };

  return (
    <div className="app-container min-h-screen bg-base-100" data-theme="ppwa">
      {/* Konten utama berdasarkan tab aktif */}
      <main className="flex-1">
        {renderContent()}
      </main>

      {/* Navigasi bawah */}
      <Navigation
        activeTab={activeTab}
        onTabChange={handleTabChange}
      />
    </div>
  )
}

export default App
